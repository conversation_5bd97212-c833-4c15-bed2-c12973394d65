# Fix timezone display in KITCO Morning Fix components

# Fix LondonFixGrid.tsx
$file1 = "C:\Users\<USER>\WebstormProjects\kitco-cms-next\next\src\components-metals\LondonFixGrid\LondonFixGrid.tsx"
$content1 = Get-Content $file1
$content1[185] = ""
$content1 | Set-Content $file1

# Fix LondonFixGridMobile.tsx  
$file2 = "C:\Users\<USER>\WebstormProjects\kitco-cms-next\next\src\components-metals\LondonFixGrid\LondonFixGridMobile.tsx"
$content2 = Get-Content $file2
$content2[225] = ""
$content2 | Set-Content $file2

# Fix useMorningFix.ts
$file3 = "C:\Users\<USER>\WebstormProjects\kitco-cms-next\next\src\hooks\MorningFix\useMorningFix.ts"
$content3 = Get-Content $file3
$content3[112] = "            const time = abbr ? ``${baseTime} ( ``${abbr} )`` : baseTime"
$content3 | Set-Content $file3

Write-Host "Fixed timezone display in all three files"
